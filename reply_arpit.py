#!/usr/bin/env python3
"""
whatsapp_check_and_reply.py

On each run, this script will:
1. Look for any unread WhatsApp message from +16674641784.
2. If found, follow the user’s instructions or answer their question.
3. Send the reply back and exit.

Prerequisites:
    pip install "praisonaiagents[llm]" mcp
    export OPENAI_API_KEY="your_openai_api_key"

Make sure:
    – Your WhatsApp bridge is running:
        cd whatsapp-mcp/whatsapp-bridge && go run main.go
    – Your MCP server is running over stdio (default):
        cd whatsapp-mcp/whatsapp-mcp-server && python main.py
"""

from praisonaiagents import Agent, MCP

# Path to your local whatsapp-mcp-server main.py
MCP_CMD = "python /Users/<USER>/Documents/Arpit/Project/whatsapp-mcp-poc/whatsapp-mcp/whatsapp-mcp-server/main.py"

# Initialize an Agent that only checks +16674641784
whatsapp_agent = Agent(
    instructions=(
        "You are a WhatsApp assistant. Check the last chat for any unread messages. If there is an unread message, read it, then either answer the question asked or carry out the instruction contained in the message, and reply with the response to the same chat. "
    ),
    llm="gpt-4o-mini",
    tools=MCP(MCP_CMD)
)

if __name__ == "__main__":
    # A single start() invocation will:
    #  1) call get_chats → find the chat for +16674641784
    #  2) call get_chat_history to fetch unread messages
    #  3) call send_message to reply
    whatsapp_agent.start("")
