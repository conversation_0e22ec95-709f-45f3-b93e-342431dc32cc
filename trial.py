#!/usr/bin/env python3
"""
whatsapp_bot_stdio.py

Uses the stdio MCP transport to talk directly to the built-in WhatsApp-MCP server.
No need for `--transport streamable-http`.

Prerequisites:
    pip install "praisonaiagents[llm]" mcp
    # (this gives you the `mcp` CLI & praisonaiagents.Agent/MCP)

    # Make sure your WhatsApp-MCP bridge is running (the Go part):
    # cd whatsapp-mcp/whatsapp-bridge && go run main.go

    # And that your Python MCP server is *not* shifted to HTTP:
    # cd whatsapp-mcp/whatsapp-mcp-server
    # python main.py            # defaults to transport='stdio'
"""

import time
from praisonaiagents import Agent, MCP

# ——— Configuration ———
# Point this at your local checkout of whatsapp-mcp-server/main.py
MCP_CMD      = "python /Users/<USER>/Documents/Arpit/Project/whatsapp-mcp-poc/whatsapp-mcp/whatsapp-mcp-server/main.py"
POLL_SECONDS = 5

# ——— Initialize your Agent with stdio-MCP ———
whatsapp_agent = Agent(
    instructions=(
        "You are a WhatsApp assistant. "
        "When a new message arrives, perform exactly the action the user requested "
        "and then reply with the result."
    ),
    llm="gpt-4o-mini",
    tools=MCP(MCP_CMD)
)

def main():
    print(f"Starting stdio-MCP bot loop (polling every {POLL_SECONDS}s)…")
    try:
        while True:
            # This single call will:
            # 1) call get_chats
            # 2) call get_chat_history for each
            # 3) call send_message as needed
            # all driven by the LLM’s reasoning.
            whatsapp_agent.start(
                "Check for any new incoming messages. "
                "For each one, do exactly what they ask (by invoking send_message, "
                "get_chats, get_chat_history, etc.) and reply with the result."
            )
            time.sleep(POLL_SECONDS)

    except KeyboardInterrupt:
        print("\nBot stopped by user.")

if __name__ == "__main__":
    main()
