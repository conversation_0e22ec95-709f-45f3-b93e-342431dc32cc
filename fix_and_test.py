#!/usr/bin/env python3
"""
Fix and Test Script for WhatsApp Listener

This script helps diagnose and fix common issues with the WhatsApp listener setup.
"""

import os
import sys

def check_environment():
    """Check and fix environment variables."""
    print("=== Environment Check ===")
    
    # Check OpenAI API Key
    api_key = os.getenv('OPENAI_API_KEY')
    if not api_key:
        print("❌ OPENAI_API_KEY is not set!")
        print("Please run: export OPENAI_API_KEY='your-api-key'")
        return False
    elif api_key == 'not-needed':
        print("❌ OPENAI_API_KEY is set to 'not-needed' - this won't work!")
        print("Please set a real OpenAI API key")
        return False
    else:
        print(f"✅ OPENAI_API_KEY is set (ends with: ...{api_key[-10:]})")
    
    # Check for conflicting base URL
    base_url = os.getenv('OPENAI_API_BASE')
    if base_url:
        print(f"⚠️  OPENAI_API_BASE is set to: {base_url}")
        print("This might cause connection issues. Removing it...")
        del os.environ['OPENAI_API_BASE']
        print("✅ OPENAI_API_BASE removed")
    else:
        print("✅ OPENAI_API_BASE is not set (good)")
    
    return True

def test_openai():
    """Test OpenAI API connection."""
    print("\n=== OpenAI API Test ===")
    try:
        from openai import OpenAI
        client = OpenAI()
        
        print("Testing OpenAI connection...")
        response = client.chat.completions.create(
            model="gpt-4o-mini",
            messages=[{"role": "user", "content": "Say 'Connection test successful!'"}],
            max_tokens=10
        )
        
        result = response.choices[0].message.content
        print(f"✅ OpenAI Response: {result}")
        return True
        
    except Exception as e:
        print(f"❌ OpenAI test failed: {e}")
        return False

def test_whatsapp_mcp():
    """Test WhatsApp MCP server connection."""
    print("\n=== WhatsApp MCP Test ===")
    try:
        from praisonaiagents import Agent, MCP
        
        MCP_CMD = "python /Users/<USER>/Documents/Arpit/Project/whatsapp-mcp-poc/whatsapp-mcp/whatsapp-mcp-server/main.py"
        
        print("Creating test agent...")
        agent = Agent(
            instructions="You are a test agent. Just respond with 'MCP connection works!'",
            llm="gpt-4o-mini",
            tools=MCP(MCP_CMD)
        )
        
        print("Testing MCP connection...")
        result = agent.start("Say 'MCP test successful'")
        print(f"✅ MCP Response: {result}")
        return True
        
    except Exception as e:
        print(f"❌ MCP test failed: {e}")
        print("Make sure the WhatsApp bridge is running:")
        print("cd whatsapp-mcp/whatsapp-bridge && go run main.go")
        return False

def main():
    """Run all tests."""
    print("WhatsApp Listener Diagnostic Tool")
    print("=" * 50)
    
    # Check environment
    if not check_environment():
        print("\n❌ Environment check failed. Please fix the issues above.")
        return
    
    # Test OpenAI
    if not test_openai():
        print("\n❌ OpenAI test failed. Please check your API key and internet connection.")
        return
    
    # Test WhatsApp MCP
    if not test_whatsapp_mcp():
        print("\n❌ WhatsApp MCP test failed. Please check the bridge is running.")
        return
    
    print("\n🎉 All tests passed! Your WhatsApp listener should work now.")
    print("\nTo run the listener:")
    print("python whatsapp_listener.py")

if __name__ == "__main__":
    main()
